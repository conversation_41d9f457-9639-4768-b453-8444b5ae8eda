# 如何重启

!!!宝塔里面的项目不要启动

切换到www用户

> 首次启动

```bash
pm2 start ./app.js --name puppeteer --cron-restart="0 0 * * *" --max-memory-restart 512M -i 4
```

> 重启

```bash
pm2 restart puppeteer
```

瓦力每次发布会自动重启

## 性能优化说明

### 主要改进

1. **页面池管理**: 限制最大页面数为5个，避免内存溢出
2. **并发控制**: 最大并发请求数限制为10个
3. **超时机制**: 页面获取超时时间为10秒，避免长时间等待
4. **内存监控**: 每30秒检查内存使用情况，自动垃圾回收

### 新增端点

- `GET /health` - 健康检查，返回服务状态和内存使用情况
- `GET /stats` - 详细统计信息
- `POST /reset-stats` - 重置统计计数器

### 错误码说明

- `408`: 请求超时
- `429`: 请求过于频繁
- `503`: 服务暂时不可用（页面池满，等待超时）

### 建议的PM2配置

```bash
pm2 start ./app.js --name puppeteer --cron-restart="0 0 * * *" --max-memory-restart 1024M -i 1 --node-args="--expose-gc"
```

注意：
- 建议单实例运行 (`-i 1`) 以更好地控制页面池
- 增加内存限制到1024M
- 添加 `--expose-gc` 参数启用垃圾回收