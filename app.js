const express = require('express');
const app = express();
const puppeteer = require('puppeteer');
const { v4: uuidv4 } = require('uuid');
const COS = require('cos-nodejs-sdk-v5');

// 配置 Puppeteer 参数
const options = {
  headless: 'new',
  executablePath: "/home/<USER>/.cache/puppeteer/chrome/linux-115.0.5790.98/chrome-linux64/chrome",
  args: [
    '--disable-extensions',
    '--disable-plugins',
    '--disable-setuid-sandbox',
    '--no-sandbox',
    '--no-zygote',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--no-first-run',
    '--disable-background-networking',
    '--disable-sync',
    '--disable-translate',
    '--hide-scrollbars',
    '--metrics-recording-only',
    '--mute-audio',
    '--safebrowsing-disable-auto-update',
    '--ignore-certificate-errors',
    '--ignore-ssl-errors',
    '--ignore-certificate-errors-spki-list',
    '--font-render-hinting=medium',
  ],
};

// 创建一个浏览器实例，复用该实例
const browserPromise = puppeteer.launch(options);

// 配置 COS 参数
const cos = new COS({
  SecretId: "AKIDPm15yj3cNyOdhZr1W4ZwHXcVDkFheFQQ",
  SecretKey: "1rW7YuAF9uVuAXpyxlPsAIOPPJ2c1EF7",
});

// 上传到 COS
function uploadToCos(filename, dataInfo) {
  return new Promise((resolve, reject) => {
    cos.putObject({
      Bucket: "dta-1252139118",
      Region: "ap-beijing",
      Key: filename,
      Body: dataInfo,
    },
    (err, data) => {
      if (err) {
        console.error(err);
        reject(err);
      } else {
        console.log(filename);
        resolve(`https://dta-1252139118.file.myqcloud.com/${filename}`);
      }
    });
  });
}

// 成功响应
function createSuccessResponse(pdfUrl, imageUrl) {
  return {
    status: true,
    code: 200,
    message: 'success',
    data: {
      pdf_url: pdfUrl || null, // 如果没有生成 PDF，返回 null
      image_url: imageUrl || null, // 如果没有生成图片，返回 null
    },
    redirect: '',
    lang: 'zh_CN',
  };
}

// 失败响应
function createFailResponse(code, message) {
  return {
    status: true,
    code: code,
    message: message,
    data: {},
    redirect: '',
    lang: 'zh_CN',
  };
}

// 处理 GET 请求
app.get('/screenshot', async (req, res) => {
  let url = req.query.url;
  let format = req.query.format || 'png'; // 默认生成 PNG 格式、
  if(format === 'image'){
      format = 'png'
  }

  const quality = req.query.quality ? parseInt(req.query.quality) : 80; // 默认质量为 80，适用于 webp 格式
  const scale = req.query.scale ? parseFloat(req.query.scale) : 1; // 默认缩放比例为 1

 try {
      if (url.includes('%')) {
        url = decodeURIComponent(url);
      }
    } catch (err) {
      console.error('Failed to decode URL:', url, err);
      return res.send(createFailResponse(400, 'Invalid URL encoding'));
    }

  try {
    // 获取浏览器实例
    const browser = await browserPromise;
    const defaultWidth = 1024;
    const defaultHeight = 768;

    let width = req.query.width ? Number(req.query.width) : defaultWidth;
    let height = req.query.height ? Number(req.query.height) : defaultHeight;

    // 打开一个新页面
    const page = await browser.newPage();
    await page.addStyleTag({
  content: `
    * {
      font-variant-numeric: tabular-nums !important;
      font-feature-settings: "tnum" on !important;
    }
  `
});
     // 添加自定义字体样式，解决数字间距问题
    if (req.query.isMobile === 'true') {
      await page.emulate(puppeteer.devices['iPhone XR']);
    }
    await page.goto(url, {
      'timeout': 1000 * 100,
      waitUntil: 'networkidle0'
    });

    // 如果有参数 dom，就动态设置 viewport，否则使用默认的 viewport
    if (req.query.dom) {
      const dom = await page.$eval(req.query.dom, (x) => {
            console.log(window.getComputedStyle(document.body).fontFamily);
        return JSON.parse(JSON.stringify(window.getComputedStyle(x)));
      });
      console.log('dom', dom.width, dom.height);
      width = Number(dom.width.slice(0, -2));
      height = Number(dom.height.slice(0, -2));
    }
    await page.evaluate(() => {
});

    if (req.query.isMobile === 'true') {
      await page.setViewport({
        width: width,
        height: height,
        isMobile: true,
        hasTouch: true,
        deviceScaleFactor: 3
      });
    } else {
      await page.setViewport({
        width: width,
        height: height,
      });
    }

    // 访问指定网页
    let pdfUrl = '';
    let imageUrl = '';
    
    // 生成 PDF 文件
    if (format === 'pdf' || format === 'all') {
      const pdf = await page.pdf({
        scale: scale, // 使用动态传入的 scale 值
        printBackground: true,
        preferCSSPageSize: true,
        format: 'A4',
        '-webkit-print-color-adjust': 'exact'
      });
      const pdfFilename = `${uuidv4()}.pdf`;
      pdfUrl = await uploadToCos(pdfFilename, pdf);
      console.log(pdfUrl);
    }
    
       // 获取当前页面 body 的字体信息
    const fontFamily = await page.evaluate(() => {
      return window.getComputedStyle(document.body).fontFamily;
    });

    console.log('Current font-family:', fontFamily);  // 打印字体信息

    // 生成图片文件 (支持 png 或 webp 格式)
if (format === 'png' || format === 'webp' || format === 'all' || format === 'image') {
    // 将 'all' 和 'image' 格式改为 'png'
    if (format === 'all' || format === 'image') {
        format = 'png';
    }

    try {
        const screenshot = await page.screenshot({
            fullPage: true,
            omitBackground: true, // 忽略背景色
            type: format          // 根据请求的格式生成截图
        });

        const imageFilename = `${uuidv4()}.${format}`;
        imageUrl = await uploadToCos(imageFilename, screenshot);
    } catch (error) {
        console.error('Screenshot or upload failed:', error);
        throw new Error('Failed to capture screenshot or upload to COS');
    }
}

    // 返回响应，PDF 和图片 URL 都可以是 null
    res.send(createSuccessResponse(pdfUrl, imageUrl));

    // 关闭页面
    await page.close();
  } catch (error) {
    console.error(error);
    res.send(createFailResponse(500, error));
  }
});

// 启动服务器
const port = 9000;
app.listen(port, () => {
  console.log(`Server started on port ${port}`);
});