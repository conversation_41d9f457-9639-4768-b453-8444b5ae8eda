const express = require('express');
const app = express();
const puppeteer = require('puppeteer');
const { v4: uuidv4 } = require('uuid');
const COS = require('cos-nodejs-sdk-v5');

// 中间件配置
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志中间件
app.use((req, res, next) => {
  const start = Date.now();
  const requestId = uuidv4();
  req.requestId = requestId;

  console.log(`[${requestId}] ${req.method} ${req.url} - ${req.ip}`);

  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(`[${requestId}] ${res.statusCode} - ${duration}ms`);
  });

  next();
});

// 配置 Puppeteer 参数
const options = {
  headless: 'new',
  // executablePath: "/home/<USER>/.cache/puppeteer/chrome/linux-115.0.5790.98/chrome-linux64/chrome",
  args: [
    '--disable-extensions',
    '--disable-plugins',
    '--disable-setuid-sandbox',
    '--no-sandbox',
    '--no-zygote',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--no-first-run',
    '--disable-background-networking',
    '--disable-sync',
    '--disable-translate',
    '--hide-scrollbars',
    '--metrics-recording-only',
    '--mute-audio',
    '--safebrowsing-disable-auto-update',
    '--ignore-certificate-errors',
    '--ignore-ssl-errors',
    '--ignore-certificate-errors-spki-list',
    '--font-render-hinting=medium',
    // 新增内存优化参数
    '--memory-pressure-off',
    '--max_old_space_size=4096',
    '--disable-background-timer-throttling',
    '--disable-renderer-backgrounding',
    '--disable-backgrounding-occluded-windows',
    '--disable-features=TranslateUI',
    '--disable-ipc-flooding-protection',
  ],
};

// 页面池配置
const PAGE_POOL_CONFIG = {
  maxPages: 5,           // 最大页面数
  maxWaitTime: 10000,    // 最大等待时间 10秒
  pageTimeout: 100000,   // 页面超时时间
  maxConcurrent: 10,     // 最大并发请求数
};

// 页面池管理
class PagePool {
  constructor(browser, config) {
    this.browser = browser;
    this.config = config;
    this.availablePages = [];
    this.busyPages = new Set();
    this.waitingQueue = [];
    this.currentRequests = 0;
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      timeoutRequests: 0,
      averageWaitTime: 0,
    };
  }

  async initialize() {
    // 预创建一些页面
    for (let i = 0; i < Math.min(2, this.config.maxPages); i++) {
      try {
        const page = await this.browser.newPage();
        await this.setupPage(page);
        this.availablePages.push(page);
      } catch (error) {
        console.error('Failed to create initial page:', error);
      }
    }
  }

  async setupPage(page) {
    // 设置页面基本配置
    await page.setDefaultTimeout(this.config.pageTimeout);
    await page.setDefaultNavigationTimeout(this.config.pageTimeout);

    // 添加字体样式
    await page.addStyleTag({
      content: `
        * {
          font-variant-numeric: tabular-nums !important;
          font-feature-settings: "tnum" on !important;
        }
      `
    });

    // 监听页面错误
    page.on('error', (error) => {
      console.error('Page error:', error);
    });

    page.on('pageerror', (error) => {
      console.error('Page script error:', error);
    });
  }

  async acquirePage() {
    const startTime = Date.now();

    // 检查并发限制
    if (this.currentRequests >= this.config.maxConcurrent) {
      throw new Error('Too many concurrent requests');
    }

    this.currentRequests++;
    this.stats.totalRequests++;

    try {
      // 如果有可用页面，直接返回
      if (this.availablePages.length > 0) {
        const page = this.availablePages.pop();
        this.busyPages.add(page);
        return page;
      }

      // 如果还能创建新页面，创建一个
      if (this.busyPages.size < this.config.maxPages) {
        const page = await this.browser.newPage();
        await this.setupPage(page);
        this.busyPages.add(page);
        return page;
      }

      // 等待页面释放
      return await this.waitForPage(startTime);
    } catch (error) {
      this.currentRequests--;
      throw error;
    }
  }

  async waitForPage(startTime) {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        // 从等待队列中移除
        const index = this.waitingQueue.findIndex(item => item.resolve === resolve);
        if (index !== -1) {
          this.waitingQueue.splice(index, 1);
        }
        this.currentRequests--;
        this.stats.timeoutRequests++;
        reject(new Error('Page acquisition timeout after 10 seconds'));
      }, this.config.maxWaitTime);

      this.waitingQueue.push({
        resolve: (page) => {
          clearTimeout(timeoutId);
          const waitTime = Date.now() - startTime;
          this.updateAverageWaitTime(waitTime);
          resolve(page);
        },
        reject: (error) => {
          clearTimeout(timeoutId);
          this.currentRequests--;
          reject(error);
        },
        startTime
      });
    });
  }

  async releasePage(page) {
    this.currentRequests--;

    try {
      // 清理页面状态
      await page.goto('about:blank');
      await page.evaluate(() => {
        // 清理可能的内存泄漏
        if (window.gc) {
          window.gc();
        }
      });

      this.busyPages.delete(page);

      // 如果有等待的请求，直接分配给它们
      if (this.waitingQueue.length > 0) {
        const waiting = this.waitingQueue.shift();
        this.busyPages.add(page);
        waiting.resolve(page);
        return;
      }

      // 否则放回可用池
      this.availablePages.push(page);

      // 如果页面太多，关闭一些
      if (this.availablePages.length > 3) {
        const pageToClose = this.availablePages.pop();
        await pageToClose.close();
      }
    } catch (error) {
      console.error('Error releasing page:', error);
      // 如果页面有问题，直接关闭
      try {
        await page.close();
      } catch (closeError) {
        console.error('Error closing problematic page:', closeError);
      }
    }
  }

  updateAverageWaitTime(waitTime) {
    if (this.stats.totalRequests === 1) {
      this.stats.averageWaitTime = waitTime;
    } else {
      this.stats.averageWaitTime = (this.stats.averageWaitTime * (this.stats.totalRequests - 1) + waitTime) / this.stats.totalRequests;
    }
  }

  getStats() {
    return {
      ...this.stats,
      availablePages: this.availablePages.length,
      busyPages: this.busyPages.size,
      waitingQueue: this.waitingQueue.length,
      currentRequests: this.currentRequests,
      totalPages: this.availablePages.length + this.busyPages.size,
    };
  }

  async cleanup() {
    // 清理所有页面
    for (const page of this.availablePages) {
      try {
        await page.close();
      } catch (error) {
        console.error('Error closing available page:', error);
      }
    }

    for (const page of this.busyPages) {
      try {
        await page.close();
      } catch (error) {
        console.error('Error closing busy page:', error);
      }
    }

    this.availablePages = [];
    this.busyPages.clear();
    this.waitingQueue = [];
  }
}

// 创建浏览器实例和页面池
let browser;
let pagePool;

async function initializeBrowser() {
  try {
    browser = await puppeteer.launch(options);
    pagePool = new PagePool(browser, PAGE_POOL_CONFIG);
    await pagePool.initialize();
    console.log('Browser and page pool initialized successfully');
  } catch (error) {
    console.error('Failed to initialize browser:', error);
    process.exit(1);
  }
}

// 启动时初始化
initializeBrowser();

// 配置 COS 参数
const cos = new COS({
  SecretId: "AKIDPm15yj3cNyOdhZr1W4ZwHXcVDkFheFQQ",
  SecretKey: "1rW7YuAF9uVuAXpyxlPsAIOPPJ2c1EF7",
});

// 上传到 COS
function uploadToCos(filename, dataInfo) {
  return new Promise((resolve, reject) => {
    cos.putObject({
      Bucket: "dta-1252139118",
      Region: "ap-beijing",
      Key: filename,
      Body: dataInfo,
    },
    (err, _data) => {
      if (err) {
        console.error('COS upload error:', err);
        reject(err);
      } else {
        console.log('File uploaded successfully:', filename);
        resolve(`https://dta-1252139118.file.myqcloud.com/${filename}`);
      }
    });
  });
}

// 成功响应
function createSuccessResponse(pdfUrl, imageUrl) {
  return {
    status: true,
    code: 200,
    message: 'success',
    data: {
      pdf_url: pdfUrl || null, // 如果没有生成 PDF，返回 null
      image_url: imageUrl || null, // 如果没有生成图片，返回 null
    },
    redirect: '',
    lang: 'zh_CN',
  };
}

// 失败响应
function createFailResponse(code, message) {
  return {
    status: true,
    code: code,
    message: message,
    data: {},
    redirect: '',
    lang: 'zh_CN',
  };
}

// 处理 GET 请求
app.get('/screenshot', async (req, res) => {
  const requestId = req.requestId || uuidv4();
  const startTime = Date.now();

  console.log(`[${requestId}] New screenshot request started`);

  let url = req.query.url;
  let format = req.query.format || 'png';
  if(format === 'image'){
      format = 'png'
  }

  const scale = req.query.scale ? parseFloat(req.query.scale) : 1;

  // URL 解码
  try {
    if (url.includes('%')) {
      url = decodeURIComponent(url);
    }
  } catch (err) {
    console.error(`[${requestId}] Failed to decode URL:`, url, err);
    return res.send(createFailResponse(400, 'Invalid URL encoding'));
  }

  let page = null;

  try {
    // 从页面池获取页面
    console.log(`[${requestId}] Acquiring page from pool...`);
    page = await pagePool.acquirePage();
    console.log(`[${requestId}] Page acquired successfully`);

    const defaultWidth = 1024;
    const defaultHeight = 768;
    let width = req.query.width ? Number(req.query.width) : defaultWidth;
    let height = req.query.height ? Number(req.query.height) : defaultHeight;

    // 移动端模拟
    if (req.query.isMobile === 'true') {
      await page.emulate(puppeteer.devices['iPhone XR']);
    }

    // 导航到目标页面
    console.log(`[${requestId}] Navigating to URL: ${url}`);
    await page.goto(url, {
      timeout: PAGE_POOL_CONFIG.pageTimeout,
      waitUntil: 'networkidle0'
    });

    // 动态设置 viewport
    if (req.query.dom) {
      try {
        const dom = await page.$eval(req.query.dom, (x) => {
          console.log(window.getComputedStyle(document.body).fontFamily);
          return JSON.parse(JSON.stringify(window.getComputedStyle(x)));
        });
        console.log(`[${requestId}] DOM dimensions:`, dom.width, dom.height);
        width = Number(dom.width.slice(0, -2));
        height = Number(dom.height.slice(0, -2));
      } catch (domError) {
        console.warn(`[${requestId}] Failed to get DOM dimensions:`, domError.message);
      }
    }

    // 设置视口
    if (req.query.isMobile === 'true') {
      await page.setViewport({
        width: width,
        height: height,
        isMobile: true,
        hasTouch: true,
        deviceScaleFactor: 3
      });
    } else {
      await page.setViewport({
        width: width,
        height: height,
      });
    }

    let pdfUrl = '';
    let imageUrl = '';

    // 生成 PDF 文件
    if (format === 'pdf' || format === 'all') {
      console.log(`[${requestId}] Generating PDF...`);
      const pdf = await page.pdf({
        scale: scale,
        printBackground: true,
        preferCSSPageSize: true,
        format: 'A4',
        '-webkit-print-color-adjust': 'exact'
      });
      const pdfFilename = `${uuidv4()}.pdf`;
      pdfUrl = await uploadToCos(pdfFilename, pdf);
      console.log(`[${requestId}] PDF generated: ${pdfUrl}`);
    }

    // 获取字体信息
    try {
      const fontFamily = await page.evaluate(() => {
        return window.getComputedStyle(document.body).fontFamily;
      });
      console.log(`[${requestId}] Font family:`, fontFamily);
    } catch (fontError) {
      console.warn(`[${requestId}] Failed to get font info:`, fontError.message);
    }

    // 生成图片文件
    if (format === 'png' || format === 'webp' || format === 'all' || format === 'image') {
      // 标准化格式
      if (format === 'all' || format === 'image') {
        format = 'png';
      }

      console.log(`[${requestId}] Generating screenshot (${format})...`);
      try {
        const screenshot = await page.screenshot({
          fullPage: true,
          omitBackground: true,
          type: format
        });

        const imageFilename = `${uuidv4()}.${format}`;
        imageUrl = await uploadToCos(imageFilename, screenshot);
        console.log(`[${requestId}] Screenshot generated: ${imageUrl}`);
      } catch (error) {
        console.error(`[${requestId}] Screenshot or upload failed:`, error);
        throw new Error('Failed to capture screenshot or upload to COS');
      }
    }

    // 更新统计
    pagePool.stats.successfulRequests++;

    const processingTime = Date.now() - startTime;
    console.log(`[${requestId}] Request completed successfully in ${processingTime}ms`);

    // 返回响应
    res.send(createSuccessResponse(pdfUrl, imageUrl));

  } catch (error) {
    pagePool.stats.failedRequests++;
    const processingTime = Date.now() - startTime;

    console.error(`[${requestId}] Request failed after ${processingTime}ms:`, error.message);

    // 根据错误类型返回不同的错误码
    let errorCode = 500;
    let errorMessage = error.message;

    if (error.message.includes('timeout')) {
      errorCode = 408;
      errorMessage = 'Request timeout';
    } else if (error.message.includes('Too many concurrent requests')) {
      errorCode = 429;
      errorMessage = 'Too many requests, please try again later';
    } else if (error.message.includes('Page acquisition timeout')) {
      errorCode = 503;
      errorMessage = 'Service temporarily unavailable, please try again later';
    }

    res.send(createFailResponse(errorCode, errorMessage));
  } finally {
    // 释放页面回池
    if (page) {
      try {
        await pagePool.releasePage(page);
        console.log(`[${requestId}] Page released back to pool`);
      } catch (releaseError) {
        console.error(`[${requestId}] Error releasing page:`, releaseError);
      }
    }
  }
});

// 全局错误处理中间件
app.use((err, req, res, _next) => {
  const requestId = req.requestId || 'unknown';
  console.error(`[${requestId}] Unhandled error:`, err);

  res.status(500).send(createFailResponse(500, 'Internal server error'));
});

// 404 处理
app.use((_req, res) => {
  res.status(404).send(createFailResponse(404, 'Endpoint not found'));
});

// 健康检查路由
app.get('/health', async (_req, res) => {
  try {
    const memoryUsage = process.memoryUsage();
    const poolStats = pagePool ? pagePool.getStats() : null;
    const uptime = process.uptime();

    // 检查浏览器状态
    let browserStatus = 'unknown';
    try {
      if (browser && browser.isConnected()) {
        browserStatus = 'connected';
      } else {
        browserStatus = 'disconnected';
      }
    } catch (error) {
      browserStatus = 'error';
    }

    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(uptime),
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
      },
      browser: {
        status: browserStatus,
        connected: browser ? browser.isConnected() : false,
      },
      pagePool: poolStats,
      config: {
        maxPages: PAGE_POOL_CONFIG.maxPages,
        maxWaitTime: PAGE_POOL_CONFIG.maxWaitTime,
        maxConcurrent: PAGE_POOL_CONFIG.maxConcurrent,
      }
    };

    // 判断服务健康状态
    const isHealthy =
      browserStatus === 'connected' &&
      (poolStats ? poolStats.currentRequests < PAGE_POOL_CONFIG.maxConcurrent : true) &&
      memoryUsage.heapUsed < 1024 * 1024 * 1024; // 1GB 内存限制

    if (!isHealthy) {
      healthData.status = 'unhealthy';
      res.status(503);
    }

    res.json(healthData);
  } catch (error) {
    console.error('Health check error:', error);
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// 统计信息路由
app.get('/stats', (_req, res) => {
  try {
    const memoryUsage = process.memoryUsage();
    const poolStats = pagePool ? pagePool.getStats() : null;

    res.json({
      memory: {
        rss: memoryUsage.rss,
        heapUsed: memoryUsage.heapUsed,
        heapTotal: memoryUsage.heapTotal,
        external: memoryUsage.external,
      },
      pagePool: poolStats,
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Stats error:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString(),
    });
  }
});

// 重置统计信息路由 (用于测试)
app.post('/reset-stats', (_req, res) => {
  try {
    if (pagePool) {
      pagePool.stats = {
        totalRequests: 0,
        successfulRequests: 0,
        failedRequests: 0,
        timeoutRequests: 0,
        averageWaitTime: 0,
      };
    }
    res.json({ message: 'Statistics reset successfully' });
  } catch (error) {
    console.error('Reset stats error:', error);
    res.status(500).json({ error: error.message });
  }
});

// 优雅关闭处理
process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, shutting down gracefully...');
  await gracefulShutdown();
});

process.on('SIGINT', async () => {
  console.log('Received SIGINT, shutting down gracefully...');
  await gracefulShutdown();
});

async function gracefulShutdown() {
  try {
    console.log('Cleaning up page pool...');
    if (pagePool) {
      await pagePool.cleanup();
    }

    console.log('Closing browser...');
    if (browser) {
      await browser.close();
    }

    console.log('Shutdown complete');
    process.exit(0);
  } catch (error) {
    console.error('Error during shutdown:', error);
    process.exit(1);
  }
}

// 定期内存监控和清理
setInterval(async () => {
  const memoryUsage = process.memoryUsage();
  const heapUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);

  console.log(`Memory usage: ${heapUsedMB}MB heap, ${Math.round(memoryUsage.rss / 1024 / 1024)}MB RSS`);

  if (pagePool) {
    const stats = pagePool.getStats();
    console.log(`Page pool: ${stats.availablePages} available, ${stats.busyPages} busy, ${stats.waitingQueue} waiting`);

    // 如果内存使用过高，强制垃圾回收
    if (heapUsedMB > 512 && global.gc) {
      console.log('High memory usage detected, forcing garbage collection...');
      global.gc();
    }
  }
}, 30000); // 每30秒检查一次

// 启动服务器
const port = 8989;
app.listen(port, () => {
  console.log(`Server started on port ${port}`);
  console.log(`Health check available at http://localhost:${port}/health`);
  console.log(`Stats available at http://localhost:${port}/stats`);
});